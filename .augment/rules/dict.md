---
type: "manual"
---

字段编码	字段描述	字段说明	备注
delivery_no	销售退货单		
customer	客户		kunnr
sales_org	销售组织		
return_date	退货日期		
return_reason	特殊退货原因		
amount	金额		
currency	币别		


字段编码	字段描述	字段说明	备注
delivery_no	销售提货单		
delivery_lineno	提货单行		posnr
sales_org	销售组织		
customer	客户号		kunnr
delivery_date	提货日期		
zzmatnr	物料号		
quantity	数量		qty
unit	单位		meins
netpr	单价		


字段编码	字段描述	字段说明	备注
torder_sqno	SAS退货申请单号	系统自动生成	XT20250001
vkorg	销售组织	按代码存表，前端显示按描述，参考销售订单	 
vtweg	分销渠道	按代码存表，前端显示按描述，参考销售订单	 
spart	产品组	按代码存表，前端显示按描述，参考销售订单	 
kunnr	客户代码	按代码存表，前端显示按描述，参考销售订单	 
name1	客户名称	退货单位，即客户名称	 
customer_type	客户类型	服务站/ 专卖店，下拉菜单，用户选择	 
return_reason	退货原因	新建配置表，配置内容：
新品投放退货
农机投放退货
   PJ退货
走账退货	参考配置表《专卖项目SAS：配置表.xlsx》待更新
return_mode	退货方式	销售退货 / 反采购，由用户选择，如果选择了销售退货，则每一行都要有来源单号，如果没有选择则报错	 
vkbur	销售办公室	按代码存表，前端显示按描述，参考销售订单	 
werks	工厂		2025-08-11新增
shipmethod	发运方式	根据客户主数据默认，可修改	20250828更新
release_status	审批状态	未审批 / 已审批，默认未审批，灰色不可修改	 
document_status	单据状态	未提交 / 已提交 / 已关闭，默认未提交，灰色不可修改	 已关闭状态下，申请单不可提交
created_date	创建时间	系统当前日	 
created_by	创建人	创建人ID，按代码存表，前端显示按描述	 


字段编码	字段描述	字段说明	备注	备注1
item_no	序号	系统按流水号自动生成	 	
zzmatnr	物料号	SAS物料号	 	
name	名称	SAS物料主数据中文物料描述	 	
allow_qty	可退数量	灰色不可修改
无来源单：0
根据来源提货单号的同一个物料号或图号在SAS外向交货单中查找所有行记录，汇总数量。再减去另外搜索出的SAS销售退货订单中该来源单号的所有数量		
apply_qty	申请退货数量	 默认数量=可退货数量，可修改	 	
kein	物料单位	无来源单：手工输入 
有来源单：物料的单位，默认带出	 	
netpr	单价	无来源单：手工输入 
有来源单：物料的单价，默认带出	 	
discount	折扣	 单位百分比%，默认100%，可修改	 	
amount	退货金额（元）	 如果同意退货数量大于0：单价*折扣*同意退货数量；否则单价*折扣*申请退货数量	 	
remark	子公司备注	 手工输入文本	 	
original_type	来源类型	字典：1. SAS提货单 2.期初数据		20250828更新
original_dn	来源提货单号	 提供搜索功能，由用户根据客户号、销售组织、物料号或图号搜索销售交货单号码，搜索分2个步骤：
1.先搜索历史发货数据表，维护在SAS的期初数据表对应表格
2.上面没有查到数据的话再继续查找销售交货单表记录	 按类型查询对应的来源	提供接口
original_item	来源提货单行号	 根据来源单和物料号自动匹配行号，不可修改	 	
original_date	提货日期	 根据来源单的创建提货日期	 	
overdue_date	超期范围	系统当前日期-来源单的提货日期，判断逻辑：
未超期：根据销售组织和客户类型选择《退货超期配置表》维护的退货时间（如果没找到就按默认30天），计算的天数小于维护的日期为未超期，
超期1年以内：大于等于未超期小于1年的
超期1-2年：大于等于1年小于2年的
超期2-3年：大于等于2年小于3年的
超期3年以上：大于等于3年的
超期的几个定义范围需要更新到字典清单	 参考配置表《专卖项目SAS：配置表.xlsx》待更新	
document_no	整改文件号	 手工输入文本	 	
return_mode	退货方式	销售退货 / 反采购，灰色不可修改，逻辑判断：有来源提货单号的是“销售退货”，否则默认“反采购”	 调整到抬头	
return_amount_90days	前90天已退货金额	 搜索90天内该客户在相同销售组织/采购组织下的退货订单退货入库单或反采购入库单数量计算金额，并且排除特殊退货原因在配置表中的数据，也是通过2个数据源
1.查找期初退货退货单的记录
2.查找销售退货单入库和反采购订单入库的金额
3.汇总合计上面的金额+本次退货申请单的金额，展示出来。灰色，不可修改	不包含特殊原因退货，配置参考字段抬头的：退货原因	2020826调整
approve_qty	同意退货数量	 灰色，不可修改，审批环节由审批人修改	 	
last_year_sales_amount	上年度销售额	 	留空	
year_sales_amount	本年累计销售额	 	留空	
year_return_amount	本年累计退货金额	 	留空	
allow_return_rate	制度可退货比例标准	 	留空	
lgort	库存地点		手工输入	


