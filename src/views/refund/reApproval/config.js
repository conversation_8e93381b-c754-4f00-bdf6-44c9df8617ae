// 使用模拟数据
import { listReOrderHis, getReOrderHis, delReOrderHis, addReOrderHis, updateReOrderHis } from "./mockApi";

export const cloumn = [
  {
    prop: "torderSqno",
    label: "退货申请单号",
    width: 180,
    align: "center",
  },
  {
    prop: "name1",
    label: "客户名称",
    width: 200,
    align: "left",
    showOverflowTooltip: true,
  },
  {
    prop: "customerType",
    label: "客户类型",
    width: 120,
    align: "center",
    optionsCode: "customer_type",
    type: "dict",
  },
  {
    prop: "returnReason",
    label: "退货原因",
    width: 150,
    align: "left",
    showOverflowTooltip: true,
    optionsCode: "return_reason",
    type: "dict",
  },
  {
    prop: "releaseStatus",
    label: "审批状态",
    width: 120,
    align: "center",
    optionsCode: "release_status",
    type: "dict",
    slotName: "releaseStatus",
  }
];

export const searchData =[
  {
    label: "退货申请单号",
    type: "input",
    prop: "torderSqno",
  },

  {
    label: "客户代码",
    type: "input",
    prop: "kunnr",
    append: true,
    dictOptionCode: "KUNNR",
    attr: {},
    backProps: [
      {
        backProp: "KUNNR",
        backLable: "KUNNR",
        prop: "kunnr",
      },
      {
        backProp: "NAME1",
        backLable: "NAME1",
        prop: "name1",
      },
    ],
  },
  {
    prop: "VKORG",
    label: "销售组织",
    width: "200px",
    append:true,
    attr: {
      readonly: true,
    },
    propName: "salesOrgName",
    dictOptionCode: "VKORG",
    //  返回的字段映射
    backProps: [
      {
        backProp: "salesOrg",
        backLable: "salesOrgName",
        prop: "VKORG",
      },
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "WERKS",
      },
    ],
  },

  {
    prop: "customerType",
    label: "客户类型",
    type: "select",
    optionsCode: "customer_type",
    width: 140,
  },

  {
    label: "审批状态",
    type: "select",
    prop: "releaseStatus",
    optionsCode: "release_status",
    // slotName: "releaseStatus",
    width: 140,
    attr: {
      // disabled: true,
    },
  },


]
export const pageConfig = {
  indexPageConfig: {
    title: "退货审批",
    name: "ReShipmentHis",
    columns: cloumn,
    searchData: searchData,
    tableProps: {
      rowKey: "reOrderId",
    },
    searchProps: {},
    headerBtn: [
        {
        name: "新增",
        icon: "el-icon-plus",
        type: "primary",
        url:'',
        clickType:'add',
        hasPermi:['sales:reOrderHis:add']
      },
      //  {
      //   name: "批量删除",
      //   icon: "el-icon-delete",
      //   type: "danger",
      //   // click: () => {},
      //   requestUrl: "",
      //   clickType:'del',
      //   hasPermi:['sales:reOrderHis:remove']


      // },
       {
        name: "导出",
        icon: "el-icon-plus",
        type: "primary",
        requestUrl: "sales/reOrderHis/export",
        clickType:'export',
        hasPermi:['sales:reOrderHis:export']
      },
    ],
    tableRowBtn: [
      {
        name: "修改",
        icon: "el-icon-edit",
        type: "text",
        requestUrl: "",
        clickType:'edit',
        hasPermi:['sales:reOrderHis:edit']

      },
      {
        name: "删除",
        icon: "el-icon-delete",
        type: "text",
        // click: () => {},
        requestUrl: "",
        clickType:'del',
        hasPermi:['sales:reOrderHis:remove']


      },
    ],
    // 列表
    getList: listReOrderHis,

    detailPageType: "modal", // 'modal'弹窗，page新开页面
    modalWidth:'50%',
  },
  addPageConfig: {
    headerFormData: JSON.parse(JSON.stringify(cloumn)).map((item)=>{
      item.span = 24
      return item
    }),
    addShowNum:cloumn.length,
    addSearchProps: {
    },
    headerSearchInfo:{},
    // 新增
    addFunction: addReOrderHis,
    // 更新
    updataFuntion: updateReOrderHis,
    // 删除
    delFunction: delReOrderHis,
    // 详情
    detailFunction: getReOrderHis,
  },
};
