# 退货审批模块 - 模拟数据说明

## 概述
由于后端接口尚未完成，本模块使用模拟数据进行前端开发和测试。

## 文件说明

### mockApi.js
- 模拟后端 API 接口
- 包含增删改查功能
- 支持搜索和分页
- 模拟网络延迟

### mockDict.js  
- 模拟字典数据
- 包含客户类型、审批状态、退货原因等字典
- 提供字典标签获取函数

### config.js
- 列配置和搜索配置
- 已修改为使用模拟 API
- 字段名称已根据 dict.md 规范调整

### index.vue
- 主页面组件
- 自动加载模拟字典数据到 store

## 模拟数据内容

### 列表数据
包含 8 条测试数据，涵盖：
- 不同的客户类型（专卖店、服务站）
- 不同的审批状态（未审批、已审批）
- 不同的退货原因
- 不同的销售组织

### 字典数据
- **客户类型**: 专卖店、服务站
- **审批状态**: 未审批、已审批  
- **退货原因**: 新品投放退货、农机投放退货、PJ退货、走账退货
- **单据状态**: 未提交、已提交、已关闭

## 切换到真实接口

当后端接口完成后，只需要：

1. 修改 `config.js` 第 2 行：
   ```javascript
   // 从
   import { listReOrderHis, getReOrderHis, delReOrderHis, addReOrderHis, updateReOrderHis } from "./mockApi";
   
   // 改为
   import { listReOrderHis, getReOrderHis, delReOrderHis, addReOrderHis, updateReOrderHis } from "@/api/sales/reOrderHis";
   ```

2. 删除 `index.vue` 中的模拟字典数据设置代码

3. 恢复 `dictsKey` 配置：
   ```javascript
   dictsKey: ["VKORG",'customer_type','release_status','return_reason']
   ```

## 功能测试

- ✅ 列表展示
- ✅ 搜索过滤
- ✅ 分页功能
- ✅ 字典显示
- ✅ 增删改查操作
- ✅ 表单验证

## 注意事项

- 模拟数据仅在当前模块内使用，不会污染其他模块
- 所有模拟文件都在 reApproval 文件夹内
- 字段名称已按照 dict.md 规范配置
- 支持完整的 CRUD 操作测试
