import request from '@/utils/request'

// 模拟数据
const mockData = [
  {
    reOrderId: '1',
    torderSqno: 'XT20250001',
    name1: '北京玉柴专卖店',
    customerType: '1',
    returnReason: '1',
    releaseStatus: '0',
    vkorg: '1000',
    kunnr: '100001',
    createdDate: '2025-01-15',
    createdBy: 'admin',
    documentStatus: '1'
  },
  {
    reOrderId: '2',
    torderSqno: 'XT20250002',
    name1: '上海玉柴服务站',
    customerType: '2',
    returnReason: '2',
    releaseStatus: '1',
    vkorg: '1000',
    kunnr: '100002',
    createdDate: '2025-01-16',
    createdBy: 'admin',
    documentStatus: '2'
  },
  {
    reOrderId: '3',
    torderSqno: 'XT20250003',
    name1: '广州玉柴专卖店',
    customerType: '1',
    returnReason: '3',
    releaseStatus: '0',
    vkorg: '2000',
    kunnr: '200001',
    createdDate: '2025-01-17',
    createdBy: 'user1',
    documentStatus: '1'
  },
  {
    reOrderId: '4',
    torderSqno: 'XT20250004',
    name1: '深圳玉柴服务站',
    customerType: '2',
    returnReason: '4',
    releaseStatus: '1',
    vkorg: '2000',
    kunnr: '200002',
    createdDate: '2025-01-18',
    createdBy: 'user2',
    documentStatus: '2'
  },
  {
    reOrderId: '5',
    torderSqno: 'XT20250005',
    name1: '成都玉柴专卖店',
    customerType: '1',
    returnReason: '1',
    releaseStatus: '0',
    vkorg: '3000',
    kunnr: '300001',
    createdDate: '2025-01-19',
    createdBy: 'admin',
    documentStatus: '1'
  }
];

// 查询期初历史退货列表
export function listReOrderHis(query) {
  // 模拟接口响应
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockData];

      // 模拟搜索过滤
      if (query.refundApplyNo) {
        filteredData = filteredData.filter(item =>
          item.torderSqno.includes(query.refundApplyNo)
        );
      }
      if (query.kunnr) {
        filteredData = filteredData.filter(item =>
          item.kunnr.includes(query.kunnr) || item.name1.includes(query.kunnr)
        );
      }
      if (query.customerType) {
        filteredData = filteredData.filter(item =>
          item.customerType === query.customerType
        );
      }
      if (query.releaseStatus) {
        filteredData = filteredData.filter(item =>
          item.releaseStatus === query.releaseStatus
        );
      }

      // 模拟分页
      const pageNum = query.pageNum || 1;
      const pageSize = query.pageSize || 10;
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;
      const rows = filteredData.slice(start, end);

      resolve({
        code: 200,
        msg: '查询成功',
        rows: rows,
        total: filteredData.length
      });
    }, 300); // 模拟网络延迟
  });

  // 真实接口调用（暂时注释）
  // return request({
  //   url: '/sales/reOrderHis/list',
  //   method: 'get',
  //   params: query
  // })
}

// 查询期初历史退货详细
export function getReOrderHis(reOrderId) {
  // 模拟接口响应
  return new Promise((resolve) => {
    setTimeout(() => {
      const item = mockData.find(item => item.reOrderId === reOrderId);
      if (item) {
        resolve({
          code: 200,
          msg: '查询成功',
          data: item
        });
      } else {
        resolve({
          code: 404,
          msg: '数据不存在'
        });
      }
    }, 200);
  });

  // 真实接口调用（暂时注释）
  // return request({
  //   url: '/sales/reOrderHis/' + reOrderId,
  //   method: 'get'
  // })
}

// 新增期初历史退货
export function addReOrderHis(data) {
  // 模拟接口响应
  return new Promise((resolve) => {
    setTimeout(() => {
      const newId = (mockData.length + 1).toString();
      const newItem = {
        ...data,
        reOrderId: newId,
        torderSqno: `XT2025${String(mockData.length + 1).padStart(4, '0')}`,
        createdDate: new Date().toISOString().split('T')[0],
        createdBy: 'admin',
        documentStatus: '1',
        releaseStatus: '0'
      };
      mockData.push(newItem);

      resolve({
        code: 200,
        msg: '新增成功',
        data: newItem
      });
    }, 300);
  });

  // 真实接口调用（暂时注释）
  // return request({
  //   url: '/sales/reOrderHis',
  //   method: 'post',
  //   data: data
  // })
}

// 修改期初历史退货
export function updateReOrderHis(data) {
  // 模拟接口响应
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockData.findIndex(item => item.reOrderId === data.reOrderId);
      if (index !== -1) {
        mockData[index] = { ...mockData[index], ...data };
        resolve({
          code: 200,
          msg: '修改成功',
          data: mockData[index]
        });
      } else {
        resolve({
          code: 404,
          msg: '数据不存在'
        });
      }
    }, 300);
  });

  // 真实接口调用（暂时注释）
  // return request({
  //   url: '/sales/reOrderHis',
  //   method: 'put',
  //   data: data
  // })
}

// 删除期初历史退货
export function delReOrderHis(reOrderId) {
  // 模拟接口响应
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockData.findIndex(item => item.reOrderId === reOrderId);
      if (index !== -1) {
        mockData.splice(index, 1);
        resolve({
          code: 200,
          msg: '删除成功'
        });
      } else {
        resolve({
          code: 404,
          msg: '数据不存在'
        });
      }
    }, 300);
  });

  // 真实接口调用（暂时注释）
  // return request({
  //   url: '/sales/reOrderHis/' + reOrderId,
  //   method: 'delete'
  // })
}
