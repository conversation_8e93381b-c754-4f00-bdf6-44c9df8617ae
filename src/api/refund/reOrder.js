import request from '@/utils/request'

// 查询销售退货申请单头信息列表
export function listReOrder(query) {
  return request({
    url: '/sales/reOrder/list',
    method: 'get',
    params: query
  })
}

// 查询销售退货申请单头信息详细
export function getReOrder(reOrderHeadId) {
  return request({
    url: '/sales/reOrder/' + reOrderHeadId,
    method: 'get'
  })
}

// 新增销售退货申请单头信息
export function addReOrder(data) {
  return request({
    url: '/sales/reOrder',
    method: 'post',
    data: data
  })
}

// 修改销售退货申请单头信息
export function updateReOrder(data) {
  return request({
    url: '/sales/reOrder',
    method: 'put',
    data: data
  })
}

// 删除销售退货申请单头信息
export function delReOrder(reOrderHeadId) {
  return request({
    url: '/sales/reOrder/' + reOrderHeadId,
    method: 'delete'
  })
}


// 获取90天汇总金额
export function getReOrderTotal(data) {
  return request({
    url: '/sales/reOrder',
    method: 'post',
    data: data
  })
}
